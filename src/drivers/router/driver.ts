import Homey from 'homey';
import PairSession from 'homey/lib/PairSession';

class RouterDriver extends Homey.Driver {
  private pendingDevice: any = null;

  /**
   * onInit is called when the driver is initialized.
   */
  async onInit() {
    this.log('Router driver has been initialized');
  }

  /**
   * Simple network discovery to find potential routers
   */
  private async discoverRouters(): Promise<any[]> {
    this.log('Starting router discovery');

    const potentialRouters = [
      { ip: '*************', name: '<PERSON>uijie X32-PRO Router (*************)' },
      { ip: '***********', name: '<PERSON><PERSON><PERSON><PERSON> X32-PRO Router (***********)' },
      { ip: '***********', name: '<PERSON><PERSON>jie X32-PRO Router (***********)' },
    ];

    const discoveredRouters = [];

    for (const router of potentialRouters) {
      try {
        this.log(`Testing connection to ${router.ip}`);

        // Try to connect to the router's web interface
        const axios = require('axios');
        const response = await axios.get(`http://${router.ip}/cgi-bin/luci/`, {
          timeout: 3000,
          validateStatus: () => true // Accept any status code
        });

        if (response.status === 200 && response.data.includes('X32-PRO')) {
          this.log(`Found Ruijie X32-PRO router at ${router.ip}`);
          discoveredRouters.push({
            name: router.name,
            data: {
              id: `router-${router.ip.replace(/\./g, '-')}`,
            },
            settings: {
              ip: router.ip,
              username: 'admin',
              password: '', // User will need to enter this
            },
          });
        }
      } catch (error) {
        this.log(`No router found at ${router.ip}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    this.log(`Discovery completed. Found ${discoveredRouters.length} routers`);
    return discoveredRouters;
  }

  /**
   * onPairListDevices is called when a user is adding a device and the 'list_devices' view is called.
   * This should return an array with the data of devices that are available for pairing.
   */
  async onPairListDevices() {
    this.log('onPairListDevices called');

    // Check if we have stored device data from the credentials step
    if (this.pendingDevice) {
      this.log('Returning stored device data:', this.pendingDevice);
      return [this.pendingDevice];
    }

    // If no pending device, try to discover routers on the network
    this.log('No pending device, attempting network discovery');
    try {
      const discoveredRouters = await this.discoverRouters();
      if (discoveredRouters.length > 0) {
        this.log(`Returning ${discoveredRouters.length} discovered routers`);
        return discoveredRouters;
      }
    } catch (error) {
      this.error('Discovery failed:', error);
    }

    this.log('No routers discovered, returning empty array');
    return [];
  }

  /**
   * Handle the pairing process
   */
  async onPair(session: PairSession): Promise<void> {
    this.log('onPair called, setting up pairing session');

    let validatedSettings = {
      ip: '',
      username: 'admin',
      password: '',
    };

    // Register listener for when the user submits router credentials
    session.setHandler('validate_router_credentials', async (data) => {
      try {
        const { ip, password } = data;
        const username = 'admin'; // Default username for Ruijie routers

        this.log(`Validating router credentials - IP: ${ip}, Password: ${password ? '******' : 'not set'}`);

        // Try to connect to the router to validate credentials
        this.log('Importing RouterApiClient');
        const { RouterApiClient } = await import('../../lib/RouterApiClient');

        this.log('Creating new RouterApiClient instance');
        const client = new RouterApiClient(ip, username, password);

        this.log('Initializing client to test connection');
        await client.init();

        // If we get here, the connection was successful
        this.log('Router connection validated successfully');

        // Store validated settings
        validatedSettings = { ip, username, password };

        // Create device data for the list_devices view
        const deviceId = `router-${ip.replace(/\./g, '-')}`;
        const deviceData = {
          name: `Ruijie X32-PRO Router (${ip})`,
          data: {
            id: deviceId,
          },
          settings: validatedSettings,
        };

        // Store device data for the list_devices view
        this.pendingDevice = deviceData;
        this.log('Stored device data for list_devices view');

        // Clean up the client
        client.stopConnectionCheck();

        return true;
      } catch (error) {
        this.error('Failed to validate router credentials:', error);
        return false;
      }
    });

    // This handler is called when the user selects a device from the list_devices view
    session.setHandler('list_devices', async () => {
      this.log('list_devices handler called');
      return await this.onPairListDevices();
    });
  }
}

module.exports = RouterDriver;
